"""
This module defines models for chat context.
"""

# Standard imports
from enum import Enum
from typing import Any, Dict, Optional, List
from uuid import UUID

# External imports
from pydantic import BaseModel, Field
from src.app.models.projects.project_models_v1 import VerdictMeta

class PredefinedPrompt(Enum):
    GENERATE_TIMELINE = 1
    GENERATE_QUESTIONS=2
    GENERATE_SUMMARY=3

class ChatRequest(BaseModel):
    """
    Model for the chat request.
    """

    session_id: str = Field(
        ...,
        description="The session ID for the chat.",
        example="46b52c5a-8426-4835-b92d-e1f10276fa56",
    )
    user_id: str = Field(
        ...,
        description="The user ID.",
        example="bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45",
    )
    user_message: Optional[str] = Field(
        None,
        description="The input message from the user.",
        example="How can I search for Dutch legal cases and learn about their outcomes?",
    )
    included_document_ids: Optional[List[UUID]] = Field(
        "[]",
        description="The document ids to include.",
        example="[18c3d27d-a3a9-4fc4-a826-6857d926e466]",
    )
    legal_areas: Optional[List[str]] = Field(
        "[Civiel recht]",
        description="The legal areas to search for. (IN DUTCH)",
        example="[Civiel recht]",
    )
    legal_sub_areas: Optional[List[str]] = Field(
        "[Verbintenissenrecht]",
        description="The legal sub-areas to search for. (IN DUTCH)",
        example="[Verbintenissenrecht]",
    )
    jurisprudence: bool = Field(
        False,
        description="Flag to enable jurisprudence search.",
        example=False,
    )
    dutch_law: bool = Field(
        False,
        description="[DISABLED] Flag to enable Dutch law search.",
        example=False,
    )

    predefined_prompt: Optional[int] = Field(
        None,
        description="""The numeric value of the enum to pick a predefined prompt. Options are:
            "GENERATE_TIMELINE"=1
            "GENERATE_QUESTIONS"=2
            "GENERATE_SUMMARY"=3
        """,
        example="3"
    )

class ChatResponse(BaseModel):
    """
    Model for the chat response.
    """

    id: str = Field(
        ...,
        description="An unique identifier provided by the LLM model.",
        example="run-f24be93a-f9e3-4bcb-8b31-66e5223d019a-0",
    )
    organization_id: UUID = Field(
        ...,
        description="The ID of the organization.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    project_id: Optional[UUID] = Field(
        None,
        description="The ID of the project.",
        example="550e8400-e29b-41d4-a716-************",
    )
    session_id: str = Field(
        ...,
        description="The session ID for the chat.",
        example="46b52c5a-8426-4835-b92d-e1f10276fa56",
    )
    user_id: str = Field(
        ...,
        description="The user ID.",
        example="bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45",
    )
    user_message: str = Field(
        ...,
        description="The input message from the user.",
        example="What is rechtspraak.nl?",
    )
    content: str = Field(
        ...,
        description="The content generated by the chat service.",
        example="Rechtspraak.nl is de officiële website van de Rechtspraak in Nederland. Deze website biedt toegang tot een breed scala aan juridische informatie en diensten.",
    )
    prompt_tokens: int = Field(
        ...,
        description="The number of tokens used in the prompt.",
        example=20,
    )
    completion_tokens: int = Field(
        ...,
        description="The number of tokens used in the completion.",
        example=30,
    )
    total_tokens: int = Field(
        ...,
        description="The total number of tokens used (prompt + completion).",
        example=50,
    )


class StreamingChunk(BaseModel):
    """Model for streaming response chunks"""

    content: str = Field(default="")
    is_final: bool = Field(default=False)
    metadata: Optional[Dict[str, Any]] = Field(default=None)


class SeedMessageIn(BaseModel):
    """
    Model for seeding a chat message.
    """

    conversation_id: str = Field(..., description="The session ID for the chat.")
    content: str = Field(..., description="The content of the message to seed.")
    role: str = Field(
        "assistant",
        description="The role of the message sender (e.g., 'assistant', 'system').",
    )
    order: Optional[int] = Field(None, description="Optional placement order.")
    verdict_meta: Optional[VerdictMeta] = None
